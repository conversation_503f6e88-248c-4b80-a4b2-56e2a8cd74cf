.container {
  position: absolute;
  width: 100%;
}

.weightDisplay {
  display: flex;
  padding: 2px;
  app-region: drag;
}

.weightValue {
  line-height: 1;
}

.weightValue {
  font-size: 28px;
  font-weight: normal;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  min-width: 59px;

  .weightInfoText {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -0.72px;
    text-align: left;
    color: #fff;
    margin-top: 3px;
  }

}
.weightValueBelowMinimum {
  font-weight: bold;
}

.weightInfo {
  display: flex;
  flex-direction: column;
}


.weightMinimum,
.weightOrder {
  font-family: Syncopate;
  font-size: 10.6px;
  font-weight: bold;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: -0.1px;
  text-align: left;
  color: #fff;
}


.showMininumWeightImg {
  // Create red warning shape using pure CSS
  background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
  position: relative;
  padding: 0px 5px;
  width: 100%;
  box-sizing: border-box;
  border-radius: 8px 0px 8px 8px; // Rounded corners for modern look

  // Create angled cut effect using pseudo-element
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 20px 56px 0; // Create triangular cut
    border-color: transparent #191a20 transparent transparent; // Match background color
  }

  // Add subtle shadow for depth
  box-shadow: 0 2px 8px rgba(255, 68, 68, 0.3);
}
.content {
  width: 100%; // Use full width instead of fixed 270px
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: bold;
  position: relative;
  box-sizing: border-box; // Ensure proper width calculation

  // Enhanced styling when red warning is active
  &.showMininumWeightImg {
    z-index: 1; // Ensure content appears above pseudo-elements

    .weightValue,
    .weightInfo {
      position: relative;
      z-index: 2; // Ensure text appears above background effects
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); // Add text shadow for better readability
    }
  }

  .weight {
    font-size: 24px;
  }

  .min-order {
    font-size: 12px;
    text-transform: uppercase;
  }
}