.container {
  position: absolute;
  width: 100%;
}

.weightDisplay {
  display: flex;
  padding: 2px;
  app-region: drag;
}

.weightValue {
  line-height: 1;
}

.weightValue {
  font-size: 28px;
  font-weight: normal;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  min-width: 59px;

  .weightInfoText {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -0.72px;
    text-align: left;
    color: #fff;
    margin-top: 3px;
  }

}
.weightValueBelowMinimum {
  font-weight: bold;
}

.weightInfo {
  display: flex;
  flex-direction: column;
}


.weightMinimum,
.weightOrder {
  font-family: Syncopate;
  font-size: 10.6px;
  font-weight: bold;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: -0.1px;
  text-align: left;
  color: #fff;
}


.showMininumWeightImg {
  // Recreate exact RedShape.svg using CSS
  position: relative;
  padding: 0px 5px;
  width: 100%;
  height: 56px;
  box-sizing: border-box;

  // Create the exact gradient from the SVG
  background: linear-gradient(
    90deg,
    white 2.5%,
    #F90505 8%,
    rgba(249, 5, 5, 0.845) 47.5%,
    rgba(249, 5, 5, 0.44) 74.9%,
    #222329 100%
  );

  // Create the exact curved shape using clip-path
  clip-path: polygon(
    3.8% 0%,     /* Top-left rounded start */
    98.5% 0%,    /* Top-right straight */
    100% 7.1%,   /* Right side curve start */
    100% 8.9%,   /* Right side curve */
    57.5% 74.1%, /* Curve down to middle-right */
    42.5% 73.2%, /* Bottom curve middle */
    26.3% 96.4%, /* Bottom curve left */
    1.6% 99.1%,  /* Bottom-left curve */
    0% 92.9%,    /* Left side curve */
    0% 17.9%     /* Left side rounded corner */
  );

  // Add inner shadow effect like the SVG filter
  box-shadow:
    inset 2px 2px 8.6px rgba(255, 255, 255, 0.49),
    0 2px 4px rgba(249, 5, 5, 0.3);
}
.content {
  width: 100%; // Use full width instead of fixed 270px
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: bold;
  position: relative;
  box-sizing: border-box; // Ensure proper width calculation

  // Enhanced styling when red warning is active
  &.showMininumWeightImg {
    z-index: 1; // Ensure content appears above pseudo-elements

    .weightValue,
    .weightInfo {
      position: relative;
      z-index: 2; // Ensure text appears above background effects
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); // Add text shadow for better readability
    }
  }

  .weight {
    font-size: 24px;
  }

  .min-order {
    font-size: 12px;
    text-transform: uppercase;
  }
}