.container {
  position: absolute;
  width: 100%;
}

.weightDisplay {
  display: flex;
  padding: 2px;
  app-region: drag;
}

.weightValue {
  line-height: 1;
}

.weightValue {
  font-size: 28px;
  font-weight: normal;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  min-width: 59px;

  .weightInfoText {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -0.72px;
    text-align: left;
    color: #fff;
    margin-top: 3px;
  }

}
.weightValueBelowMinimum {
  font-weight: bold;
}

.weightInfo {
  display: flex;
  flex-direction: column;
}


.weightMinimum,
.weightOrder {
  font-family: Syncopate;
  font-size: 10.6px;
  font-weight: bold;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: -0.1px;
  text-align: left;
  color: #fff;
}


.showMininumWeightImg {
  background-image: url(../../assets/New-images/RedShape.svg);
  background-repeat: no-repeat;
  background-size: 100% 100%; // Scale image to fit full width and height
  background-position: center; // Center the background image
  padding: 0px 5px;
  width: 100%; // Ensure full width
  box-sizing: border-box; // Include padding in width calculation
}
.content {
  width: 100%; // Use full width instead of fixed 270px
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: bold;
  position: relative;
  box-sizing: border-box; // Ensure proper width calculation

  // Ensure red background scales properly when applied
  &.showMininumWeightImg {
    background-size: 100% 100% !important; // Force full width scaling
    background-position: center !important; // Center positioning
  }

  .weight {
    font-size: 24px;
  }

  .min-order {
    font-size: 12px;
    text-transform: uppercase;
  }
}