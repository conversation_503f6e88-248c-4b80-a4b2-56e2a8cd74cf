.container {
  width: 100%;
  // Ensure full width usage in right side window
  box-sizing: border-box;
}

.weightContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  // Ensure full width expansion
  box-sizing: border-box;
}

.barsContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 147px;
  padding: 10px;
  border-radius: 13px;
  background-color: rgba(255, 255, 255, 0.04);
  // Ensure full width usage
  box-sizing: border-box;
  // Remove any potential margin that might prevent full width
  margin: 0;
}

.title {
  margin: 4px 0 4px 0px;
  font-family: Inter;
  font-size: 14px;
  font-weight: 200;
  line-height: 1;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  display: flex;
  span{
    letter-spacing: 10.1px;
    &:last-child{
      margin-left: 11px;
    }
  }
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: #6b7280;
}

.bracketInfo {
  font-size: 0.875rem;
  text-align: center;
  color: #d1d5db;
  margin-bottom: 1rem;

  .bracketValue {
    color: #4ade80;
    font-weight: 700;
  }
} 