.container {
  width: 100%;
  min-height: 447px;
  padding: 16px;
  background-color: #191a20;
  background-origin: border-box;
  position: relative;
  overflow: hidden;
  // Ensure full width usage in right side window
  box-sizing: border-box;

  // Ensure PricingBracket gets full width
  > .fadeIn {
    width: 100%;
    box-sizing: border-box;
  }
}

.summarySection {
  margin: 12px 0px 0px 0px;
  background: url(../../assets/New-images/MaterialTotal.svg) no-repeat transparent;
  padding: 10px;
  background-size: cover;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0px;
  }

  .summaryRowLbl {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
  }

  .summaryRowNum {
    font-family: Inter;
    font-size: 18px;
    line-height: 1;
    text-align: right;
    color: #fff;
  }

  &.muted {

    .summaryRowLbl,
    .summaryRowNum {
      color: rgba(255, 255, 255, 0.4);
    }

  }

  &.total {
    padding: 1rem 0;
    font-weight: 600;
    font-size: 1.125rem;
  }
}

.totalPurchase {
  width: 100%;
  padding: 6px 10px 10px;
  background-image: linear-gradient(354deg, #000 142%, #191a20 4%);
  border-radius: 0px 0px 13px 13px;
  margin-top: -1px;

  .totalPurchaseLbl {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
  }

  .totalPurchaseNum {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: right;
    color: #fff;
  }
}

.disclaimer {
  margin: 12px 0px 16px 0px;
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  line-height: normal;
  letter-spacing: -0.36px;
  text-align: center;
  color: rgba(255, 255, 255, 0.4);
}

.disclaimerSavedBom {
  margin: 12px 10px;
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.67;
  letter-spacing: normal;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.netTermsContainer {
  border: 1px solid #374151;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
  cursor: pointer;
  position: relative;
}

.netTermsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.netTermsAmount {
  color: #4ade80;
  font-weight: 600;
}

.netTermsIcon {
  color: #9ca3af;
  transition: transform 0.2s;

  &.open {
    transform: rotate(180deg);
  }
}

.netTermsDropdown {
  position: absolute;
  left: 0;
  top: 100%;
  margin-top: 0.25rem;
  width: 100%;
  background-color: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.375rem;
  padding: 0.5rem;
  z-index: 10;
}

.netTermsOption {
  padding: 0.5rem;
  border-radius: 0.375rem;

  &:hover {
    background-color: #374151;
  }
}

.orderButton.orderButton {
  width: 100%;
  height: 58px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8px 0 0;
  text-align: center;
  text-transform: uppercase;
  transition: all 0.1s;
  background-position: center;
  position: relative;
  opacity: unset;

  .defaultImg,
  .HoverImg,
  .disabledImg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: fadeOut 0.1s forwards;
    display: flex;
  }

  .defaultImg {
    opacity: 1;
    animation: fadeIn 0.1s forwards;
  }

  &:hover:not(:disabled) {
    .defaultImg {
      animation: fadeOut 0.1s forwards;
    }

    .HoverImg {
      animation: fadeIn 0.1s forwards;
    }
  }

  &:disabled {

    .defaultImg,
    .HoverImg {
      animation: fadeOut 0.1s forwards;
    }

    .disabledImg {
      animation: fadeIn 0.1s forwards;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}


.updatePricingButton.updatePricingButton {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  transition: all 0.1s;
  opacity: unset;
  padding: 14px 23px 13px 24px;
  border-radius: 8px;
  box-shadow: 0 5.5px 47.4px -36.5px rgba(255, 0, 153, 0.12), 1.8px 2.7px 3.6px 0 rgba(0, 0, 0, 0.8);
  background-image: linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
  font-family: Syncopate;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.72px;
  text-align: center;
  color: #fff;
  margin-top: 58px;

  &:hover {
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)), linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
  }

  &:focus {
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)), linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
  }
}

// Animation classes
.fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.slideUp {
  // animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menuSlideUp {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdownList {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdownDataMain.dropdownDataMain {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .paymentMethod {
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.3;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
  }

  .paymentValue {
    display: flex;
    justify-content: center;
    align-self: stretch;
    flex-grow: 0;
    font-family: Inter;
    font-size: 15px;
    font-weight: normal;
    line-height: 1.3;
    text-align: center;
    color: #32ff6c;
  }
}

.dropdownList.dropdownList {
  border-radius: 10px;
  position: relative;
  padding: 8px;
  border-radius: 10px;
  -webkit-backdrop-filter: blur(22.7px);
  backdrop-filter: blur(22.7px);
  background-color: rgba(113, 115, 127, 0.7);
  max-width: 280px;

  .muiMenuList {
    padding: 0px;
    z-index: 2;

    li {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: left;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 3px;
      padding: 8px 12px;
      text-align: left;
      height: 41px;
      width: 100%;
      justify-content: flex-start;

      div {
        display: flex;
        flex-direction: column;
        row-gap: 3px;

        i {
          font-weight: 300;
          font-size: 10px;
        }
      }

      &:hover {
        border-radius: 6.8px;
        background-color: #c3c4ca;
        color: #0f0f14;

        font-weight: bold;
      }

      &:focus {
        border-radius: 6.8px;
        background-color: #c3c4ca;
        color: #0f0f14;

        font-weight: bold;
      }
    }
  }
}

.creditLimitTooltip.creditLimitTooltip {
  padding: 10px 25px;
  max-width: 282px;
  height: 60px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.15);
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.7px;
  text-align: center;
  color: #fff;
  margin-bottom: 16px !important;

  .tooltipArrow {
    color: rgba(255, 255, 255, 0.15);
  }
}